import time
from collections import deque
from typing import Any, Generator, Optional
from unittest.mock import <PERSON><PERSON><PERSON>, patch

import pytest
from flask import Flask

from core.app.entities.app_invoke_entities import Invoke<PERSON>rom
from core.workflow.entities.node_entities import NodeRunResult, WorkflowNodeExecutionMetadataKey
from core.workflow.entities.variable_pool import Variable<PERSON>ool
from core.workflow.entities.workflow_node_execution import WorkflowNodeExecutionStatus
from core.workflow.enums import SystemVariable<PERSON>ey
from core.workflow.graph_engine.entities.event import (
    GraphRunStartedEvent,
    GraphRunSucceededEvent,
    NodeRunStartedEvent,
    NodeRunSucceededEvent,
)
from core.workflow.graph_engine.entities.graph import Graph
from core.workflow.graph_engine.entities.graph_runtime_state import GraphRuntimeState
from core.workflow.graph_engine.entities.runtime_route_state import RouteNodeState
from core.workflow.graph_engine.graph_engine import GraphEngine
from core.workflow.nodes.code.code_node import CodeNode
from core.workflow.nodes.end.end_node import EndNode
from core.workflow.nodes.event import Run<PERSON>ompleted<PERSON><PERSON>
from core.workflow.nodes.llm.node import LLMNode
from core.workflow.nodes.start.start_node import StartNode
from models.enums import UserFrom
from models.workflow import WorkflowType


@pytest.fixture
def app():
    app = Flask(__name__)
    return app


class QueueBasedGraphEngine(GraphEngine):
    """
    New queue-based scheduling implementation for GraphEngine.
    
    This implementation maintains an execution queue of nodes that currently meet
    the execution criteria. At initialization, the queue contains only the StartNode.
    Execute nodes one by one, and after each node finishes execution, check if there
    are new nodes that meet execution criteria and add them to the execution queue,
    until the execution queue is empty.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.execution_queue: deque[str] = deque()
        self.executed_nodes: set[str] = set()
        
    def _initialize_execution_queue(self) -> None:
        """Initialize the execution queue with the start node."""
        self.execution_queue.clear()
        self.executed_nodes.clear()
        # Add start node to queue
        self.execution_queue.append(self.graph.root_node_id)
        
    def _check_node_execution_criteria(self, node_id: str) -> bool:
        """
        Check if a node meets execution criteria.
        
        A node meets execution criteria if:
        1. It hasn't been executed yet
        2. All its dependencies (incoming edges) have been satisfied
        3. Any run conditions on incoming edges are met
        """
        if node_id in self.executed_nodes:
            return False
            
        # Get all incoming edges to this node
        incoming_edges = []
        for source_node_id, edges in self.graph.edge_mapping.items():
            for edge in edges:
                if edge.target_node_id == node_id:
                    incoming_edges.append((source_node_id, edge))
        
        # If no incoming edges, node is ready (like start node)
        if not incoming_edges:
            return True
            
        # Check if all dependencies are satisfied
        for source_node_id, edge in incoming_edges:
            # Source node must be executed
            if source_node_id not in self.executed_nodes:
                return False
                
            # Check run condition if exists
            if edge.run_condition:
                # Get the route state of the source node
                source_route_state = None
                for route_state in self.graph_runtime_state.node_run_state.node_state_mapping.values():
                    if route_state.node_id == source_node_id:
                        source_route_state = route_state
                        break
                        
                if not source_route_state:
                    return False
                    
                # Check the run condition
                from core.workflow.graph_engine.condition_handlers.condition_manager import ConditionManager
                condition_handler = ConditionManager.get_condition_handler(
                    init_params=self.init_params,
                    graph=self.graph,
                    run_condition=edge.run_condition,
                )
                
                if not condition_handler.check(
                    graph_runtime_state=self.graph_runtime_state,
                    previous_route_node_state=source_route_state,
                ):
                    return False
                    
        return True
        
    def _find_ready_nodes(self) -> list[str]:
        """Find all nodes that are ready for execution."""
        ready_nodes = []
        for node_id in self.graph.node_ids:
            if self._check_node_execution_criteria(node_id):
                ready_nodes.append(node_id)
        return ready_nodes
        
    def _add_ready_nodes_to_queue(self) -> None:
        """Add newly ready nodes to the execution queue."""
        ready_nodes = self._find_ready_nodes()
        for node_id in ready_nodes:
            if node_id not in self.execution_queue:
                self.execution_queue.append(node_id)
                
    def _run_with_queue_scheduling(
        self,
        handle_exceptions: Optional[list[str]] = None,
    ) -> Generator[Any, None, None]:
        """
        Run the graph using queue-based scheduling.
        """
        if handle_exceptions is None:
            handle_exceptions = []
            
        # Initialize execution queue with start node
        self._initialize_execution_queue()
        
        # Process nodes in the queue until empty
        while self.execution_queue:
            # Get next node from queue
            current_node_id = self.execution_queue.popleft()
            
            # Skip if already executed (shouldn't happen with proper logic)
            if current_node_id in self.executed_nodes:
                continue
                
            # Create node instance
            node_config = None
            for node in self.graph.nodes:
                if node["id"] == current_node_id:
                    node_config = node
                    break
                    
            if not node_config:
                continue
                
            # Create route state for this node
            route_node_state = self.graph_runtime_state.node_run_state.create_node_state(current_node_id)
            
            # Create node instance
            from core.workflow.nodes.node_mapping import NODE_TYPE_CLASSES_MAPPING
            node_type = node_config.get("data", {}).get("type")
            node_cls = NODE_TYPE_CLASSES_MAPPING.get(node_type)
            
            if not node_cls:
                continue
                
            node_instance = node_cls(
                id=current_node_id,
                config=node_config,
                graph_init_params=self.init_params,
                graph=self.graph,
                graph_runtime_state=self.graph_runtime_state,
            )
            
            # Execute the node
            yield from self._run_node_with_queue(
                node_instance=node_instance,
                route_node_state=route_node_state,
                handle_exceptions=handle_exceptions,
            )
            
            # Mark node as executed
            self.executed_nodes.add(current_node_id)
            
            # Check for newly ready nodes and add them to queue
            self._add_ready_nodes_to_queue()
            
    def _run_node_with_queue(
        self,
        node_instance: Any,
        route_node_state: RouteNodeState,
        handle_exceptions: list[str],
    ) -> Generator[Any, None, None]:
        """Run a single node and handle its events."""
        # Trigger node run start event
        yield NodeRunStartedEvent(
            route_node_state=route_node_state,
            parallel_id=None,
            parallel_start_node_id=None,
            parent_parallel_id=None,
            parent_parallel_start_node_id=None,
        )
        
        # Run the node
        try:
            node_generator = node_instance.run()
            for event in node_generator:
                if hasattr(event, 'run_result'):
                    # Set the run result in route state
                    route_node_state.set_finished(event.run_result)
                    
                    # Trigger node run succeeded event
                    yield NodeRunSucceededEvent(
                        route_node_state=route_node_state,
                        parallel_id=None,
                        parallel_start_node_id=None,
                        parent_parallel_id=None,
                        parent_parallel_start_node_id=None,
                    )
                else:
                    yield event
                    
        except Exception as e:
            # Handle node execution failure
            route_node_state.status = RouteNodeState.Status.FAILED
            route_node_state.failed_reason = str(e)
            handle_exceptions.append(str(e))


def test_queue_based_scheduling_simple_linear_workflow(app):
    """Test queue-based scheduling with a simple linear workflow: start -> llm -> end"""
    
    graph_config = {
        "edges": [
            {
                "id": "start-llm",
                "source": "start",
                "target": "llm",
            },
            {
                "id": "llm-end",
                "source": "llm", 
                "target": "end",
            },
        ],
        "nodes": [
            {
                "id": "start",
                "data": {
                    "type": "start",
                    "title": "Start",
                },
            },
            {
                "id": "llm",
                "data": {
                    "type": "llm",
                    "title": "LLM",
                },
            },
            {
                "id": "end",
                "data": {
                    "type": "end",
                    "title": "End",
                },
            },
        ],
    }
    
    graph = Graph.init(graph_config=graph_config)
    
    variable_pool = VariablePool(
        system_variables={
            SystemVariableKey.QUERY: "test query",
            SystemVariableKey.FILES: [],
            SystemVariableKey.CONVERSATION_ID: "test-conv",
            SystemVariableKey.USER_ID: "test-user",
        },
        user_inputs={},
    )
    
    graph_runtime_state = GraphRuntimeState(variable_pool=variable_pool, start_at=time.perf_counter())
    
    # Use the new queue-based engine
    graph_engine = QueueBasedGraphEngine(
        tenant_id="test-tenant",
        app_id="test-app",
        workflow_type=WorkflowType.WORKFLOW,
        workflow_id="test-workflow",
        graph_config=graph_config,
        user_id="test-user",
        user_from=UserFrom.ACCOUNT,
        invoke_from=InvokeFrom.WEB_APP,
        call_depth=0,
        graph=graph,
        graph_runtime_state=graph_runtime_state,
        max_execution_steps=500,
        max_execution_time=1200,
    )
    
    # Mock node executions
    def mock_start_run():
        yield RunCompletedEvent(
            run_result=NodeRunResult(
                status=WorkflowNodeExecutionStatus.SUCCEEDED,
                inputs={},
                outputs={"query": "test query"},
            )
        )

    def mock_llm_run():
        yield RunCompletedEvent(
            run_result=NodeRunResult(
                status=WorkflowNodeExecutionStatus.SUCCEEDED,
                inputs={"query": "test query"},
                outputs={"text": "LLM response"},
                metadata={
                    WorkflowNodeExecutionMetadataKey.TOTAL_TOKENS: 10,
                },
            )
        )

    def mock_end_run():
        yield RunCompletedEvent(
            run_result=NodeRunResult(
                status=WorkflowNodeExecutionStatus.SUCCEEDED,
                inputs={"text": "LLM response"},
                outputs={"result": "LLM response"},
            )
        )
    
    with app.app_context():
        with patch.object(StartNode, "_run", new=mock_start_run):
            with patch.object(LLMNode, "_run", new=mock_llm_run):
                with patch.object(EndNode, "_run", new=mock_end_run):
                    
                    # Test the queue-based execution
                    events = list(graph_engine._run_with_queue_scheduling())
                    
                    # Verify execution order and events
                    node_started_events = [e for e in events if isinstance(e, NodeRunStartedEvent)]
                    node_succeeded_events = [e for e in events if isinstance(e, NodeRunSucceededEvent)]
                    
                    # Should have 3 nodes executed in order
                    assert len(node_started_events) == 3
                    assert len(node_succeeded_events) == 3
                    
                    # Verify execution order: start -> llm -> end
                    execution_order = [e.route_node_state.node_id for e in node_started_events]
                    assert execution_order == ["start", "llm", "end"]
                    
                    # Verify all nodes were marked as executed
                    assert graph_engine.executed_nodes == {"start", "llm", "end"}
                    
                    # Verify queue is empty at the end
                    assert len(graph_engine.execution_queue) == 0


def test_queue_based_scheduling_initialization():
    """Test that the queue-based engine initializes correctly."""
    
    # Simple graph with just start and end nodes
    graph_config = {
        "edges": [{"id": "start-end", "source": "start", "target": "end"}],
        "nodes": [
            {"id": "start", "data": {"type": "start", "title": "Start"}},
            {"id": "end", "data": {"type": "end", "title": "End"}},
        ],
    }
    
    graph = Graph.init(graph_config=graph_config)
    variable_pool = VariablePool(
        system_variables={SystemVariableKey.QUERY: "test"},
        user_inputs={},
    )
    graph_runtime_state = GraphRuntimeState(variable_pool=variable_pool, start_at=time.perf_counter())
    
    engine = QueueBasedGraphEngine(
        tenant_id="test",
        app_id="test",
        workflow_type=WorkflowType.WORKFLOW,
        workflow_id="test",
        graph_config=graph_config,
        user_id="test",
        user_from=UserFrom.ACCOUNT,
        invoke_from=InvokeFrom.WEB_APP,
        call_depth=0,
        graph=graph,
        graph_runtime_state=graph_runtime_state,
        max_execution_steps=500,
        max_execution_time=1200,
    )
    
    # Test initialization
    engine._initialize_execution_queue()
    
    # Should have start node in queue
    assert len(engine.execution_queue) == 1
    assert engine.execution_queue[0] == "start"
    assert len(engine.executed_nodes) == 0


def test_queue_based_scheduling_node_criteria_checking():
    """Test the node execution criteria checking logic."""
    
    # Graph with conditional branching
    graph_config = {
        "edges": [
            {"id": "start-node1", "source": "start", "target": "node1"},
            {"id": "node1-node2", "source": "node1", "target": "node2"},
            {"id": "start-node3", "source": "start", "target": "node3"},
        ],
        "nodes": [
            {"id": "start", "data": {"type": "start"}},
            {"id": "node1", "data": {"type": "code"}},
            {"id": "node2", "data": {"type": "code"}},
            {"id": "node3", "data": {"type": "code"}},
        ],
    }
    
    graph = Graph.init(graph_config=graph_config)
    variable_pool = VariablePool(
        system_variables={SystemVariableKey.QUERY: "test"},
        user_inputs={},
    )
    graph_runtime_state = GraphRuntimeState(variable_pool=variable_pool, start_at=time.perf_counter())
    
    engine = QueueBasedGraphEngine(
        tenant_id="test",
        app_id="test", 
        workflow_type=WorkflowType.WORKFLOW,
        workflow_id="test",
        graph_config=graph_config,
        user_id="test",
        user_from=UserFrom.ACCOUNT,
        invoke_from=InvokeFrom.WEB_APP,
        call_depth=0,
        graph=graph,
        graph_runtime_state=graph_runtime_state,
        max_execution_steps=500,
        max_execution_time=1200,
    )
    
    # Initially, only start node should meet criteria
    assert engine._check_node_execution_criteria("start") is True
    assert engine._check_node_execution_criteria("node1") is False
    assert engine._check_node_execution_criteria("node2") is False
    assert engine._check_node_execution_criteria("node3") is False
    
    # After start is executed, node1 and node3 should be ready
    engine.executed_nodes.add("start")
    assert engine._check_node_execution_criteria("node1") is True
    assert engine._check_node_execution_criteria("node2") is False  # Still depends on node1
    assert engine._check_node_execution_criteria("node3") is True
    
    # After node1 is executed, node2 should be ready
    engine.executed_nodes.add("node1")
    assert engine._check_node_execution_criteria("node2") is True


def test_queue_based_scheduling_parallel_execution():
    """Test queue-based scheduling with parallel branches."""

    # Graph with parallel execution: start -> (llm1, llm2) -> end
    graph_config = {
        "edges": [
            {"id": "start-llm1", "source": "start", "target": "llm1"},
            {"id": "start-llm2", "source": "start", "target": "llm2"},
            {"id": "llm1-end", "source": "llm1", "target": "end"},
            {"id": "llm2-end", "source": "llm2", "target": "end"},
        ],
        "nodes": [
            {"id": "start", "data": {"type": "start", "title": "Start"}},
            {"id": "llm1", "data": {"type": "llm", "title": "LLM1"}},
            {"id": "llm2", "data": {"type": "llm", "title": "LLM2"}},
            {"id": "end", "data": {"type": "end", "title": "End"}},
        ],
    }

    graph = Graph.init(graph_config=graph_config)
    variable_pool = VariablePool(
        system_variables={
            SystemVariableKey.QUERY: "test query",
            SystemVariableKey.FILES: [],
            SystemVariableKey.CONVERSATION_ID: "test-conv",
            SystemVariableKey.USER_ID: "test-user",
        },
        user_inputs={},
    )

    graph_runtime_state = GraphRuntimeState(variable_pool=variable_pool, start_at=time.perf_counter())

    engine = QueueBasedGraphEngine(
        tenant_id="test-tenant",
        app_id="test-app",
        workflow_type=WorkflowType.WORKFLOW,
        workflow_id="test-workflow",
        graph_config=graph_config,
        user_id="test-user",
        user_from=UserFrom.ACCOUNT,
        invoke_from=InvokeFrom.WEB_APP,
        call_depth=0,
        graph=graph,
        graph_runtime_state=graph_runtime_state,
        max_execution_steps=500,
        max_execution_time=1200,
    )

    # Test queue behavior with parallel nodes
    engine._initialize_execution_queue()

    # Initially only start node
    assert list(engine.execution_queue) == ["start"]

    # After start is executed, both llm1 and llm2 should be ready
    engine.executed_nodes.add("start")
    engine._add_ready_nodes_to_queue()

    # Queue should contain llm1 and llm2 (order may vary)
    queue_contents = list(engine.execution_queue)
    assert len(queue_contents) == 2
    assert "llm1" in queue_contents
    assert "llm2" in queue_contents

    # After both llm nodes are executed, end should be ready
    engine.executed_nodes.add("llm1")
    engine.executed_nodes.add("llm2")
    engine._add_ready_nodes_to_queue()

    # End node should be in queue
    assert "end" in engine.execution_queue


def test_queue_based_scheduling_conditional_branching():
    """Test queue-based scheduling with conditional branching."""

    # Graph with conditional execution using run conditions
    from core.workflow.graph_engine.entities.run_condition import RunCondition

    graph_config = {
        "edges": [
            {"id": "start-if", "source": "start", "target": "if_node"},
            {
                "id": "if-true",
                "source": "if_node",
                "target": "true_branch",
                "sourceHandle": "true"
            },
            {
                "id": "if-false",
                "source": "if_node",
                "target": "false_branch",
                "sourceHandle": "false"
            },
        ],
        "nodes": [
            {"id": "start", "data": {"type": "start"}},
            {"id": "if_node", "data": {"type": "if-else"}},
            {"id": "true_branch", "data": {"type": "code"}},
            {"id": "false_branch", "data": {"type": "code"}},
        ],
    }

    graph = Graph.init(graph_config=graph_config)

    # Add run conditions to edges
    for edge in graph.edge_mapping.get("if_node", []):
        if edge.target_node_id == "true_branch":
            edge.run_condition = RunCondition(type="branch_identify", branch_identify="true")
        elif edge.target_node_id == "false_branch":
            edge.run_condition = RunCondition(type="branch_identify", branch_identify="false")

    variable_pool = VariablePool(
        system_variables={SystemVariableKey.QUERY: "test"},
        user_inputs={},
    )
    graph_runtime_state = GraphRuntimeState(variable_pool=variable_pool, start_at=time.perf_counter())

    engine = QueueBasedGraphEngine(
        tenant_id="test",
        app_id="test",
        workflow_type=WorkflowType.WORKFLOW,
        workflow_id="test",
        graph_config=graph_config,
        user_id="test",
        user_from=UserFrom.ACCOUNT,
        invoke_from=InvokeFrom.WEB_APP,
        call_depth=0,
        graph=graph,
        graph_runtime_state=graph_runtime_state,
        max_execution_steps=500,
        max_execution_time=1200,
    )

    # Test that conditional nodes are not ready until conditions are met
    engine.executed_nodes.add("start")
    engine.executed_nodes.add("if_node")

    # Without proper route state and run result, conditional nodes shouldn't be ready
    assert engine._check_node_execution_criteria("true_branch") is False
    assert engine._check_node_execution_criteria("false_branch") is False


def test_queue_based_scheduling_error_handling():
    """Test queue-based scheduling handles node execution errors properly."""

    graph_config = {
        "edges": [
            {"id": "start-failing", "source": "start", "target": "failing_node"},
            {"id": "failing-end", "source": "failing_node", "target": "end"},
        ],
        "nodes": [
            {"id": "start", "data": {"type": "start"}},
            {"id": "failing_node", "data": {"type": "code"}},
            {"id": "end", "data": {"type": "end"}},
        ],
    }

    graph = Graph.init(graph_config=graph_config)
    variable_pool = VariablePool(
        system_variables={SystemVariableKey.QUERY: "test"},
        user_inputs={},
    )
    graph_runtime_state = GraphRuntimeState(variable_pool=variable_pool, start_at=time.perf_counter())

    engine = QueueBasedGraphEngine(
        tenant_id="test",
        app_id="test",
        workflow_type=WorkflowType.WORKFLOW,
        workflow_id="test",
        graph_config=graph_config,
        user_id="test",
        user_from=UserFrom.ACCOUNT,
        invoke_from=InvokeFrom.WEB_APP,
        call_depth=0,
        graph=graph,
        graph_runtime_state=graph_runtime_state,
        max_execution_steps=500,
        max_execution_time=1200,
    )

    def mock_start_run():
        yield RunCompletedEvent(
            run_result=NodeRunResult(
                status=WorkflowNodeExecutionStatus.SUCCEEDED,
                inputs={},
                outputs={},
            )
        )

    def mock_failing_run():
        raise Exception("Node execution failed")

    def mock_end_run():
        yield RunCompletedEvent(
            run_result=NodeRunResult(
                status=WorkflowNodeExecutionStatus.SUCCEEDED,
                inputs={},
                outputs={},
            )
        )

    with patch.object(StartNode, "_run", new=mock_start_run):
        with patch.object(CodeNode, "_run", new=mock_failing_run):
            with patch.object(EndNode, "_run", new=mock_end_run):

                # Execute and collect events
                events = list(engine._run_with_queue_scheduling())

                # Should have start node executed successfully
                start_events = [e for e in events if isinstance(e, NodeRunStartedEvent) and e.route_node_state.node_id == "start"]
                assert len(start_events) == 1

                # Should have failing node attempted
                failing_events = [e for e in events if isinstance(e, NodeRunStartedEvent) and e.route_node_state.node_id == "failing_node"]
                assert len(failing_events) == 1

                # Verify error handling doesn't crash the engine
                assert "start" in engine.executed_nodes
                assert "failing_node" in engine.executed_nodes  # Marked as executed even if failed


def test_queue_based_scheduling_complex_workflow():
    """Test queue-based scheduling with a more complex workflow structure."""

    # Complex workflow: start -> parallel(llm1, llm2) -> merge -> end
    graph_config = {
        "edges": [
            {"id": "start-llm1", "source": "start", "target": "llm1"},
            {"id": "start-llm2", "source": "start", "target": "llm2"},
            {"id": "llm1-merge", "source": "llm1", "target": "merge"},
            {"id": "llm2-merge", "source": "llm2", "target": "merge"},
            {"id": "merge-end", "source": "merge", "target": "end"},
        ],
        "nodes": [
            {"id": "start", "data": {"type": "start"}},
            {"id": "llm1", "data": {"type": "llm"}},
            {"id": "llm2", "data": {"type": "llm"}},
            {"id": "merge", "data": {"type": "code"}},
            {"id": "end", "data": {"type": "end"}},
        ],
    }

    graph = Graph.init(graph_config=graph_config)
    variable_pool = VariablePool(
        system_variables={SystemVariableKey.QUERY: "test"},
        user_inputs={},
    )
    graph_runtime_state = GraphRuntimeState(variable_pool=variable_pool, start_at=time.perf_counter())

    engine = QueueBasedGraphEngine(
        tenant_id="test",
        app_id="test",
        workflow_type=WorkflowType.WORKFLOW,
        workflow_id="test",
        graph_config=graph_config,
        user_id="test",
        user_from=UserFrom.ACCOUNT,
        invoke_from=InvokeFrom.WEB_APP,
        call_depth=0,
        graph=graph,
        graph_runtime_state=graph_runtime_state,
        max_execution_steps=500,
        max_execution_time=1200,
    )

    # Test execution flow step by step
    engine._initialize_execution_queue()

    # Step 1: Only start node ready
    ready_nodes = engine._find_ready_nodes()
    assert ready_nodes == ["start"]

    # Step 2: After start, llm1 and llm2 ready
    engine.executed_nodes.add("start")
    ready_nodes = engine._find_ready_nodes()
    assert set(ready_nodes) == {"llm1", "llm2"}

    # Step 3: After llm1 only, merge not ready yet
    engine.executed_nodes.add("llm1")
    ready_nodes = engine._find_ready_nodes()
    assert "merge" not in ready_nodes

    # Step 4: After both llm1 and llm2, merge ready
    engine.executed_nodes.add("llm2")
    ready_nodes = engine._find_ready_nodes()
    assert "merge" in ready_nodes

    # Step 5: After merge, end ready
    engine.executed_nodes.add("merge")
    ready_nodes = engine._find_ready_nodes()
    assert "end" in ready_nodes
